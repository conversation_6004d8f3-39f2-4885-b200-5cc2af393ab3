// +page.server.ts
import type { PageServerLoad } from './$types';
import { error, fail } from '@sveltejs/kit';
import type { Actions } from './$types';
import { getBackendUrl } from '$lib/config';

// export const load: PageServerLoad = async ({ cookies, params }) => {
// 	const access_token = cookies.get('access_token');
// 	const liffId = params.id;
	
// 	if (!access_token) {
// 		return {
// 			liffId,
// 			error: 'No access token available'
// 		};
// 	}

// 	// Optional: Load any initial data needed for the form
// 	// such as agreement versions, purposes, etc.
// 	try {
// 		// You can add API calls here to load form configuration data
// 		// const backendUrl = getBackendUrl();
// 		// const response = await fetch(`${backendUrl}/consent/api/agreements/`, {
// 		//     headers: {
// 		//         'Authorization': `Bearer ${access_token}`
// 		//     }
// 		// });
		
// 		return {
// 			liffId,
// 			token: access_token
// 		};
// 	} catch (err) {
// 		console.error('Error loading consent form data:', err);
// 		return {
// 			liffId,
// 			error: 'Failed to load form data'
// 		};
// 	}
// };

export const actions: Actions = {
	submit_consent: async ({ request, cookies }) => {
		// const access_token = cookies.get('access_token');
		
		// if (!access_token) {
		// 	return fail(401, { error: 'Unauthorized: No access token found' });
		// }

		try {
			const formData = await request.formData();

			// Extract form data
			const consentData = {
				// Personal Information
				first_name: formData.get('first_name') as string,
				last_name: formData.get('last_name') as string,
				national_id: formData.get('national_id') as string,
				nationality: formData.get('nationality') as string || 'Thailand',
				date_of_birth: formData.get('date_of_birth') as string,
				phone: formData.get('phone') as string,
				customer_type: formData.get('customer_type') as string,
				
				// LINE Information
				line_user_id: formData.get('line_user_id') as string,
				line_display_name: formData.get('line_display_name') as string,
				line_picture_url: formData.get('line_picture_url') as string,
				liff_id: formData.get('liff_id') as string,
				
				// Agreements - Default structure for basic consent
				agreements: [
					{
						agreement_version_id: 1,
						accepted: true,
						purposes: [
							{ purpose_id: 1, accepted: true },
							// { purpose_id: 2, accepted: true },
							// { purpose_id: 3, accepted: true }
						]
					}
				]
			};

			// Validate required fields
			const requiredFields = [
				'first_name', 'last_name', 'national_id', 'date_of_birth', 
				'phone', 'customer_type', 'line_user_id', 'liff_id'
			];

			for (const field of requiredFields) {
				if (!consentData[field]) {
					return fail(400, { 
						error: `Missing required field: ${field}`,
						field_error: field
					});
				}
			}

			// Validate national ID format (13 digits)
			const cleanNationalId = consentData.national_id.replace(/\D/g, '');
			if (cleanNationalId.length !== 13) {
				return fail(400, { 
					error: 'National ID must be 13 digits',
					field_error: 'national_id'
				});
			}

			// Validate phone number format
			const cleanPhone = consentData.phone.replace(/\D/g, '');
			if (cleanPhone.length !== 10) {
				return fail(400, { 
					error: 'Phone number must be 10 digits',
					field_error: 'phone'
				});
			}

			// Validate customer type
			const validCustomerTypes = ['CUSTOMER', 'AGENT', 'BROKER'];
			if (!validCustomerTypes.includes(consentData.customer_type)) {
				return fail(400, { 
					error: 'Invalid customer type',
					field_error: 'customer_type'
				});
			}

			console.log('Submitting consent data:', JSON.stringify(consentData, null, 2));

			// Submit to backend API
			const backendUrl = getBackendUrl();
			const response = await fetch(`${backendUrl}/consent/api/consent/submit/`, {
				method: 'POST',
				headers: {
					// 'Authorization': `Bearer ${access_token}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(consentData)
			});

			const responseData = await response.json();

			if (!response.ok) {
				console.error('Backend response error:', responseData);
				return fail(response.status, { 
					error: responseData.message || responseData.detail || 'Failed to submit consent',
					backend_error: responseData
				});
			}

			console.log('Consent submitted successfully:', responseData);

			return { 
				success: true, 
				message: 'Consent submitted successfully',
				data: responseData
			};

		} catch (error) {
			console.error('Error in submit_consent action:', error);
			
			if (error instanceof TypeError && error.message.includes('fetch')) {
				return fail(503, { 
					error: 'Unable to connect to backend service. Please try again later.'
				});
			}
			
			return fail(500, { 
				error: 'Internal server error occurred while submitting consent'
			});
		}
	}
};